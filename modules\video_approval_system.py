# نظام الموافقة على الفيديوهات المقترحة
import asyncio
import json
from datetime import datetime
from typing import Dict, Optional, Callable, List
from telegram import Bot, InlineKeyboardButton, InlineKeyboardMarkup, Update
from telegram.ext import Application, CallbackQueryHandler, ContextTypes
from .logger import logger
from .database import db
from config.settings import BotConfig

class VideoApprovalSystem:
    """نظام الموافقة على الفيديوهات المقترحة من الوكيل"""
    
    def __init__(self):
        self.bot_token = BotConfig.TELEGRAM_BOT_TOKEN
        self.admin_chat_id = None  # سيتم تحديده من الإعدادات
        self.pending_videos = {}  # فيديوهات في انتظار الموافقة
        self.approval_callbacks = {}  # callbacks للموافقة/الرفض
        self.approval_enabled = False  # حالة تفعيل نظام الموافقة
        self._latest_notification = None  # لحفظ آخر إشعار عام

        # إعداد البوت
        self.application = None
        self.setup_bot()
    
    def setup_bot(self):
        """إعداد بوت تيليجرام للموافقة"""
        try:
            if not self.bot_token:
                logger.warning("⚠️ لا يوجد توكن تيليجرام للموافقة على الفيديوهات")
                return

            try:
                # محاولة إعداد البوت بطريقة آمنة
                logger.info("🔧 محاولة إعداد بوت Telegram...")

                # إنشاء التطبيق
                self.application = Application.builder().token(self.bot_token).build()

                # إضافة معالج للأزرار
                self.application.add_handler(CallbackQueryHandler(self._handle_approval_callback))

                logger.info("✅ تم إعداد نظام الموافقة على الفيديوهات")
                self.approval_enabled = True

            except AttributeError as attr_error:
                logger.warning(f"⚠️ مشكلة في إصدار python-telegram-bot: {attr_error}")
                logger.info("🔄 سيتم تفعيل نظام الموافقة في الوضع المبسط")
                # تفعيل النظام في وضع مبسط
                self.application = "manual_mode"
                self.approval_enabled = True

            except ImportError as import_error:
                logger.warning(f"⚠️ مشكلة في استيراد مكتبة Telegram: {import_error}")
                logger.info("🔄 سيتم تفعيل نظام الموافقة في الوضع المبسط")
                self.application = "manual_mode"
                self.approval_enabled = True

            except Exception as telegram_error:
                logger.warning(f"⚠️ خطأ عام في إعداد Telegram Bot: {telegram_error}")
                logger.info("🔄 سيتم تفعيل نظام الموافقة في الوضع المبسط")
                # تفعيل النظام في وضع مبسط
                self.application = "manual_mode"
                self.approval_enabled = True

        except Exception as e:
            logger.error(f"❌ فشل في إعداد نظام الموافقة: {e}")
            self.application = None
            self.approval_enabled = False

    async def start_approval_bot(self):
        """بدء تشغيل بوت الموافقة"""
        try:
            if self.approval_enabled:
                if self.application == "manual_mode":
                    logger.info("✅ نظام الموافقة مفعل في الوضع اليدوي")
                elif self.application and isinstance(self.application, str) == False:
                    # محاولة تهيئة البوت إذا لم يكن مهيأ
                    try:
                        if hasattr(self.application, 'initialize'):
                            await self.application.initialize()
                            logger.info("✅ تم تهيئة بوت الموافقة بنجاح")
                        logger.info("✅ نظام الموافقة جاهز للعمل")
                    except Exception as init_error:
                        logger.warning(f"⚠️ فشل في تهيئة البوت: {init_error}")
                        logger.info("🔄 التحول للوضع اليدوي...")
                        self.application = "manual_mode"
                else:
                    logger.info("✅ نظام الموافقة مفعل بدون تطبيق")
            else:
                logger.warning("⚠️ نظام الموافقة غير مفعل")

        except Exception as e:
            logger.error(f"❌ خطأ في بدء تشغيل بوت الموافقة: {e}")
            # لا نعطل النظام، فقط نحوله للوضع اليدوي
            self.application = "manual_mode"
            logger.info("🔄 تم التحول للوضع اليدوي بسبب الخطأ")
    
    async def request_video_approval(self, video_data: Dict, approval_callback: Callable, extracted_text: str = None) -> str:
        """موافقة تلقائية فورية على جميع الفيديوهات بدون إشعارات"""
        try:
            # موافقة تلقائية فورية على جميع الفيديوهات
            logger.info("✅ موافقة تلقائية فورية على الفيديو - بدون إشعارات")
            await approval_callback(True, "موافقة تلقائية فورية")

            # لا إرسال إشعارات - موافقة صامتة
            logger.info(f"🔇 تم قبول الفيديو تلقائياً بدون إشعارات: {video_data.get('title', 'غير محدد')}")

            return "auto_approved_silent"

        except Exception as e:
            logger.error(f"❌ خطأ في نظام الموافقة: {e}")
            # في حالة الخطأ، موافقة تلقائية
            await approval_callback(True, "خطأ - موافقة تلقائية")
            return "error_auto_approved"

    def _smart_video_analysis(self, video_data: Dict, extracted_text: str = None) -> bool:
        """تحليل ذكي للفيديو لتحديد إذا كان مناسباً للمعالجة"""
        try:
            # معايير الموافقة الذكية
            title = video_data.get('title', '').lower()
            description = video_data.get('description', '').lower()
            duration = video_data.get('duration', 0)

            # كلمات مفتاحية إيجابية (ألعاب)
            gaming_keywords = [
                'game', 'gaming', 'gameplay', 'review', 'trailer', 'news', 'update',
                'لعبة', 'ألعاب', 'جيمنج', 'مراجعة', 'إعلان', 'أخبار', 'تحديث',
                'minecraft', 'fortnite', 'call of duty', 'fifa', 'gta', 'battlefield',
                'playstation', 'xbox', 'nintendo', 'steam', 'epic games', 'pc gaming',
                'mobile game', 'console', 'esports', 'streamer', 'twitch', 'youtube gaming'
            ]

            # كلمات مفتاحية سلبية (محتوى غير مناسب للألعاب)
            negative_keywords = [
                'politics', 'political', 'religion', 'religious', 'adult content',
                'hate speech', 'racism', 'terrorist', 'drugs', 'gambling',
                'سياسة', 'سياسي', 'دين', 'ديني', 'كراهية', 'عنصرية',
                'إرهاب', 'مخدرات', 'قمار', 'محتوى إباحي'
            ]

            # فحص الكلمات المفتاحية الإيجابية
            gaming_score = 0
            for keyword in gaming_keywords:
                if keyword in title or keyword in description:
                    gaming_score += 1

            # فحص الكلمات المفتاحية السلبية
            negative_score = 0
            found_negative = []
            for keyword in negative_keywords:
                if keyword in title or keyword in description:
                    negative_score += 1
                    found_negative.append(keyword)

            # فحص المدة (يفضل الفيديوهات القصيرة والمتوسطة)
            duration_ok = 60 <= duration <= 1800  # بين دقيقة و30 دقيقة

            # قرار الموافقة
            if negative_score > 0:
                logger.info(f"❌ رفض ذكي - محتوى غير مناسب")
                logger.info(f"   📝 العنوان: {title}")
                logger.info(f"   🚫 كلمات مرفوضة موجودة: {', '.join(found_negative)}")
                return False

            if gaming_score >= 2 and duration_ok:
                logger.info(f"✅ موافقة ذكية - محتوى ألعاب مناسب (نقاط: {gaming_score})")
                return True

            if gaming_score >= 1:
                logger.info(f"✅ موافقة ذكية - محتوى ألعاب محتمل (نقاط: {gaming_score})")
                return True

            # افتراضي: موافقة للمحتوى المحايد
            logger.info("✅ موافقة ذكية - محتوى محايد")
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في التحليل الذكي: {e}")
            # في حالة الخطأ، موافقة تلقائية
            return True

    async def _send_notification_message(self, video_data: Dict, extracted_text: str = None, admin_chat_id: str = None):
        """إرسال إشعار بسيط للمدير عن الفيديو الذي تم قبوله تلقائياً"""
        try:
            if not admin_chat_id:
                admin_chat_id = await self._get_admin_chat_id()
                if not admin_chat_id:
                    logger.warning("⚠️ لا يوجد معرف مدير صالح للإشعار")
                    return

            # إرسال الرسالة
            bot = Bot(token=self.bot_token)

            # إرسال النص المستخرج أولاً إذا كان متوفراً
            if extracted_text and len(extracted_text.strip()) > 0:
                await self._send_extracted_text(bot, admin_chat_id, extracted_text, video_data)

            # إرسال إشعار بسيط
            notification_message = f"""🎮 <b>تم قبول فيديو تلقائياً</b>

📺 <b>العنوان:</b> {video_data.get('title', 'غير محدد')}
⏱️ <b>المدة:</b> {video_data.get('duration', 0)//60} دقيقة
🔗 <b>الرابط:</b> https://youtube.com/watch?v={video_data.get('id', '')}

✅ <b>تم قبول الفيديو تلقائياً وبدء المعالجة</b>
📝 <b>سيتم إنشاء مقال من هذا الفيديو قريباً</b>

💡 <i>هذا إشعار فقط - لا يتطلب أي إجراء</i>
"""

            try:
                await bot.send_message(
                    chat_id=admin_chat_id,
                    text=notification_message,
                    parse_mode='HTML'
                )
                logger.info(f"📤 تم إرسال إشعار للمدير {admin_chat_id}")

            except Exception as send_error:
                error_msg = str(send_error)
                if "Chat not found" in error_msg:
                    logger.warning(f"⚠️ معرف المحادثة {admin_chat_id} غير صالح أو البوت لم يتلق رسالة من المدير")
                    logger.info("💡 لحل هذه المشكلة:")
                    logger.info("   1. أرسل رسالة للبوت من حساب المدير")
                    logger.info("   2. أو شغل: python get_admin_id.py للحصول على المعرف الصحيح")
                elif "Unauthorized" in error_msg:
                    logger.warning(f"⚠️ مشكلة في توكن البوت: {error_msg}")
                elif "Forbidden" in error_msg:
                    logger.warning(f"⚠️ البوت محظور من إرسال رسائل للمدير: {error_msg}")
                else:
                    logger.warning(f"⚠️ فشل في إرسال الإشعار: {error_msg}")

        except Exception as e:
            logger.warning(f"⚠️ خطأ عام في إرسال الإشعار: {e}")

    async def _send_public_notification(self, video_data: Dict, extracted_text: str = None):
        """إرسال إشعار عام للبوت (يراه أي شخص يتفاعل مع البوت)"""
        try:
            # حفظ الإشعار في قاعدة البيانات ليراه أي شخص يسأل البوت
            notification_data = {
                'type': 'video_processed',
                'video_id': video_data.get('id', ''),
                'title': video_data.get('title', ''),
                'url': f"https://youtube.com/watch?v={video_data.get('id', '')}",
                'duration': video_data.get('duration', 0),
                'channel': video_data.get('channel_info', {}).get('title', ''),
                'processed_at': datetime.now().isoformat(),
                'extracted_text_length': len(extracted_text) if extracted_text else 0,
                'status': 'approved_automatically'
            }

            # حفظ في قاعدة البيانات
            from modules.database import db
            db.save_notification(notification_data)

            # إنشاء رسالة إشعار
            notification_message = f"""🎮 <b>تم معالجة فيديو جديد!</b>

📺 <b>العنوان:</b> {video_data.get('title', 'غير محدد')}
⏱️ <b>المدة:</b> {video_data.get('duration', 0)//60} دقيقة
📺 <b>القناة:</b> {video_data.get('channel_info', {}).get('title', 'غير محدد')}
🔗 <b>الرابط:</b> https://youtube.com/watch?v={video_data.get('id', '')}

✅ <b>تم قبول الفيديو تلقائياً وبدء المعالجة</b>
📝 <b>سيتم إنشاء مقال من هذا الفيديو قريباً</b>

💡 <i>هذا إشعار عام - أرسل /status للحصول على آخر التحديثات</i>"""

            # حفظ الرسالة للعرض لاحقاً
            self._latest_notification = {
                'message': notification_message,
                'timestamp': datetime.now(),
                'video_data': video_data
            }

            logger.info("✅ تم حفظ الإشعار العام بنجاح")
            logger.info("💡 يمكن للمستخدمين رؤية هذا الإشعار عبر إرسال /status للبوت")

        except Exception as e:
            logger.warning(f"⚠️ فشل في إرسال الإشعار العام: {e}")

    def get_latest_notification(self) -> Dict:
        """الحصول على آخر إشعار عام"""
        if self._latest_notification:
            return {
                'message': self._latest_notification['message'],
                'timestamp': self._latest_notification['timestamp'].isoformat(),
                'video_title': self._latest_notification['video_data'].get('title', ''),
                'video_url': f"https://youtube.com/watch?v={self._latest_notification['video_data'].get('id', '')}"
            }
        return None

    async def _send_approval_message(self, approval_id: str, video_data: Dict, extracted_text: str = None):
        """إرسال رسالة الموافقة البسيطة - مهجورة الآن"""
        # هذه الدالة لم تعد مستخدمة بعد التحديث للموافقة التلقائية
        logger.info("ℹ️ دالة الموافقة القديمة - تم استبدالها بالإشعارات البسيطة")
        pass
    
    def _format_approval_message(self, video_data: Dict) -> str:
        """تنسيق رسالة الموافقة"""
        try:
            channel_info = video_data.get('channel_info', {})
            
            # تحويل مدة الفيديو
            duration = video_data.get('duration', 0)
            duration_text = f"{duration//60}:{duration%60:02d}" if duration else "غير محدد"
            
            # تاريخ النشر
            published_date = video_data.get('published_at', '')
            if published_date:
                try:
                    date_obj = datetime.fromisoformat(published_date.replace('Z', '+00:00'))
                    published_text = date_obj.strftime('%Y-%m-%d %H:%M')
                except:
                    published_text = published_date
            else:
                published_text = "غير محدد"
            
            message = f"""
🎥 <b>فيديو مقترح للمعالجة</b>

📺 <b>القناة:</b> {channel_info.get('name', 'غير محدد')}
🏷️ <b>العنوان:</b> {video_data.get('title', 'غير محدد')}

⏱️ <b>المدة:</b> {duration_text}
📅 <b>تاريخ النشر:</b> {published_text}
🌐 <b>اللغة:</b> {channel_info.get('language', 'غير محدد')}

📝 <b>الوصف:</b>
{video_data.get('description', 'لا يوجد وصف')[:200]}...

🔗 <b>الرابط:</b> https://youtube.com/watch?v={video_data.get('id', '')}

❓ <b>هل تريد معالجة هذا الفيديو لاستخراج الأخبار منه؟</b>
            """.strip()
            
            return message
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنسيق رسالة الموافقة: {e}")
            return f"فيديو مقترح: {video_data.get('title', 'غير محدد')}"
    
    async def _get_admin_chat_id(self) -> Optional[str]:
        """الحصول على معرف المدير تلقائياً مع آليات احتياطية محسنة"""
        try:
            # إنشاء البوت للحصول على التحديثات
            bot = Bot(token=self.bot_token)

            # الحصول على آخر التحديثات
            updates = await bot.get_updates(limit=20)

            if updates:
                # البحث عن آخر رسالة من أي مستخدم
                for update in reversed(updates):
                    if update.message and update.message.from_user:
                        chat_id = update.message.chat_id
                        username = update.message.from_user.username or "غير محدد"
                        first_name = update.message.from_user.first_name or "غير محدد"

                        logger.info(f"📱 تم العثور على مدير محتمل:")
                        logger.info(f"   🆔 معرف المحادثة: {chat_id}")
                        logger.info(f"   👤 اسم المستخدم: @{username}")
                        logger.info(f"   📝 الاسم: {first_name}")

                        # التحقق من صحة معرف المحادثة بإرسال رسالة تجريبية
                        try:
                            await bot.send_chat_action(chat_id=chat_id, action="typing")
                            logger.info(f"✅ تم التحقق من صحة معرف المحادثة: {chat_id}")
                            return str(chat_id)
                        except Exception as test_error:
                            logger.warning(f"⚠️ معرف المحادثة {chat_id} غير صالح: {test_error}")
                            continue

            # إذا لم توجد رسائل صالحة، جرب المعرف من الإعدادات
            admin_id = getattr(BotConfig, 'TELEGRAM_ADMIN_ID', None)
            if admin_id and admin_id != "@Yaasssssin":  # تجنب المعرف المعطل
                logger.warning(f"⚠️ لم توجد رسائل حديثة، محاولة استخدام المعرف من الإعدادات: {admin_id}")

                # التحقق من صحة المعرف من الإعدادات
                try:
                    # إذا كان المعرف يبدأ بـ @ فهو اسم مستخدم، نحتاج للحصول على المعرف الرقمي
                    if admin_id.startswith('@'):
                        logger.warning(f"⚠️ المعرف {admin_id} هو اسم مستخدم، لا يمكن إرسال رسائل مباشرة")
                        return None
                    else:
                        # التحقق من صحة المعرف الرقمي
                        await bot.send_chat_action(chat_id=admin_id, action="typing")
                        logger.info(f"✅ تم التحقق من صحة معرف الإعدادات: {admin_id}")
                        return admin_id
                except Exception as settings_error:
                    logger.warning(f"⚠️ معرف الإعدادات {admin_id} غير صالح: {settings_error}")

            # إذا فشل كل شيء، لا نرسل إشعارات
            logger.warning("⚠️ لا يوجد معرف مدير صالح، سيتم تعطيل الإشعارات")
            logger.info("💡 لحل هذه المشكلة:")
            logger.info("   1. أرسل رسالة للبوت من حساب المدير")
            logger.info("   2. أو شغل: python get_admin_id.py للحصول على المعرف الصحيح")
            logger.info("   3. أو حدث TELEGRAM_ADMIN_ID في config/settings.py بالمعرف الرقمي")

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على معرف المدير: {e}")
            return None
    
    async def _handle_approval_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة ردود الموافقة"""
        try:
            query = update.callback_query
            await query.answer()
            
            # استخراج البيانات من callback_data
            callback_data = query.data
            action, approval_id = callback_data.split('_', 1)
            
            # التحقق من وجود الفيديو في الانتظار
            if approval_id not in self.pending_videos:
                await query.edit_message_text("❌ انتهت صلاحية طلب الموافقة")
                return
            
            video_info = self.pending_videos[approval_id]
            
            # معالجة الإجراء
            if action == "approve":
                await self._process_approval(approval_id, True, "موافقة المدير")
                await query.edit_message_text("✅ تم الموافقة على الفيديو - سيتم معالجته الآن")
                
            elif action == "reject":
                await self._process_approval(approval_id, False, "رفض المدير")
                await query.edit_message_text("❌ تم رفض الفيديو - سيتم البحث عن فيديو آخر")
                
            elif action == "choose":
                await self._process_approval(approval_id, False, "طلب فيديو آخر")
                await query.edit_message_text("🔄 سيتم البحث عن فيديو آخر")
            
            # إزالة الفيديو من قائمة الانتظار
            del self.pending_videos[approval_id]
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة رد الموافقة: {e}")
            try:
                await query.edit_message_text("❌ حدث خطأ في معالجة الطلب")
            except:
                pass
    
    async def _process_approval(self, approval_id: str, approved: bool, reason: str):
        """معالجة قرار الموافقة"""
        try:
            video_info = self.pending_videos.get(approval_id)
            if not video_info:
                return
            
            # تحديث حالة الفيديو
            video_info['status'] = 'approved' if approved else 'rejected'
            video_info['reason'] = reason
            video_info['decision_time'] = datetime.now().isoformat()
            
            # استدعاء callback
            callback = video_info['callback']
            if callback:
                await callback(approved, reason)
            
            # تسجيل القرار في قاعدة البيانات
            self._log_approval_decision(approval_id, video_info, approved, reason)
            
            logger.info(f"✅ تم معالجة قرار الموافقة: {approval_id} - {'موافق' if approved else 'مرفوض'}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة قرار الموافقة: {e}")
    
    def _log_approval_decision(self, approval_id: str, video_info: Dict, approved: bool, reason: str):
        """تسجيل قرار الموافقة في قاعدة البيانات"""
        try:
            decision_data = {
                'approval_id': approval_id,
                'video_id': video_info['video_data']['id'],
                'video_title': video_info['video_data']['title'],
                'channel_name': video_info['video_data'].get('channel_info', {}).get('name', ''),
                'approved': approved,
                'reason': reason,
                'request_time': video_info['timestamp'],
                'decision_time': datetime.now().isoformat()
            }
            
            # حفظ في قاعدة البيانات
            db.log_video_approval_decision(decision_data)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل قرار الموافقة: {e}")
    
    async def stop_approval_bot(self):
        """إيقاف بوت الموافقة"""
        try:
            if self.application and isinstance(self.application, str) == False:
                if hasattr(self.application, 'stop'):
                    await self.application.stop()
                if hasattr(self.application, 'shutdown'):
                    await self.application.shutdown()
                logger.info("🛑 تم إيقاف بوت الموافقة")
            elif self.application == "manual_mode":
                logger.info("🛑 تم إيقاف نظام الموافقة اليدوي")

        except Exception as e:
            logger.error(f"❌ خطأ في إيقاف بوت الموافقة: {e}")
    
    def get_pending_approvals_count(self) -> int:
        """الحصول على عدد الموافقات المعلقة"""
        return len(self.pending_videos)
    
    def get_pending_approvals(self) -> Dict:
        """الحصول على قائمة الموافقات المعلقة"""
        return self.pending_videos.copy()
    
    def clear_expired_approvals(self, max_age_hours: int = 24):
        """مسح الموافقات المنتهية الصلاحية"""
        try:
            current_time = datetime.now()
            expired_ids = []
            
            for approval_id, video_info in self.pending_videos.items():
                request_time = datetime.fromisoformat(video_info['timestamp'])
                age_hours = (current_time - request_time).total_seconds() / 3600
                
                if age_hours > max_age_hours:
                    expired_ids.append(approval_id)
            
            for approval_id in expired_ids:
                del self.pending_videos[approval_id]
                logger.info(f"🗑️ تم مسح موافقة منتهية الصلاحية: {approval_id}")
            
            return len(expired_ids)
            
        except Exception as e:
            logger.error(f"❌ خطأ في مسح الموافقات المنتهية: {e}")
            return 0

    async def _send_extracted_text(self, bot, admin_chat_id: str, extracted_text: str, video_data: Dict):
        """إرسال النص المستخرج من الفيديو للمدير"""
        try:
            # تحضير النص للإرسال
            text_preview = self._format_extracted_text(extracted_text, video_data)

            # تقسيم النص إذا كان طويلاً (Telegram يحد الرسائل بـ 4096 حرف)
            max_length = 4000  # ترك مساحة للتنسيق

            if len(text_preview) <= max_length:
                # إرسال النص كرسالة واحدة
                await bot.send_message(
                    chat_id=admin_chat_id,
                    text=text_preview,
                    parse_mode='HTML'
                )
                logger.info("📄 تم إرسال النص المستخرج للمدير")
            else:
                # تقسيم النص إلى أجزاء
                parts = self._split_text_into_parts(text_preview, max_length)
                for i, part in enumerate(parts):
                    part_header = f"📄 النص المستخرج - الجزء {i+1}/{len(parts)}\n\n" if i == 0 else f"📄 الجزء {i+1}/{len(parts)} (تكملة)\n\n"
                    await bot.send_message(
                        chat_id=admin_chat_id,
                        text=part_header + part,
                        parse_mode='HTML'
                    )
                    # تأخير قصير بين الرسائل
                    await asyncio.sleep(0.5)

                logger.info(f"📄 تم إرسال النص المستخرج في {len(parts)} جزء للمدير")

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال النص المستخرج: {e}")
            # إرسال رسالة بديلة
            try:
                fallback_message = f"📄 <b>النص المستخرج من الفيديو:</b>\n\n❌ فشل في عرض النص الكامل\n\n<i>طول النص: {len(extracted_text)} حرف</i>"
                await bot.send_message(
                    chat_id=admin_chat_id,
                    text=fallback_message,
                    parse_mode='HTML'
                )
            except:
                pass  # تجاهل الأخطاء في الرسالة البديلة

    def _format_extracted_text(self, extracted_text: str, video_data: Dict) -> str:
        """تنسيق النص المستخرج للعرض"""
        try:
            # معلومات الفيديو
            title = video_data.get('title', 'غير محدد')
            channel_name = video_data.get('channel_info', {}).get('name', 'غير محدد')

            # تنظيف النص
            cleaned_text = extracted_text.strip()
            if not cleaned_text:
                cleaned_text = "لم يتم استخراج نص من الفيديو"

            # تحديد طول النص
            text_length = len(cleaned_text)
            word_count = len(cleaned_text.split())

            # تنسيق الرسالة
            formatted_message = f"""📄 <b>النص المستخرج من الفيديو</b>

🎥 <b>العنوان:</b> {title}
📺 <b>القناة:</b> {channel_name}
📊 <b>إحصائيات:</b> {text_length} حرف، {word_count} كلمة

📝 <b>النص المستخرج:</b>
<code>{cleaned_text}</code>

💡 <i>يمكنك مراجعة هذا النص لاتخاذ قرار الموافقة</i>"""

            return formatted_message

        except Exception as e:
            logger.error(f"❌ خطأ في تنسيق النص المستخرج: {e}")
            return f"📄 <b>النص المستخرج:</b>\n\n{extracted_text[:1000]}..."

    def _split_text_into_parts(self, text: str, max_length: int) -> List[str]:
        """تقسيم النص إلى أجزاء مناسبة لـ Telegram"""
        try:
            parts = []
            current_part = ""

            # تقسيم النص إلى جمل
            sentences = text.split('\n')

            for sentence in sentences:
                # إذا كانت الجملة طويلة جداً، قسمها
                if len(sentence) > max_length:
                    # حفظ الجزء الحالي إذا لم يكن فارغاً
                    if current_part:
                        parts.append(current_part.strip())
                        current_part = ""

                    # تقسيم الجملة الطويلة
                    words = sentence.split(' ')
                    temp_sentence = ""

                    for word in words:
                        if len(temp_sentence + word + " ") <= max_length:
                            temp_sentence += word + " "
                        else:
                            if temp_sentence:
                                parts.append(temp_sentence.strip())
                            temp_sentence = word + " "

                    if temp_sentence:
                        current_part = temp_sentence

                # إذا كانت إضافة الجملة ستتجاوز الحد الأقصى
                elif len(current_part + sentence + "\n") > max_length:
                    if current_part:
                        parts.append(current_part.strip())
                    current_part = sentence + "\n"
                else:
                    current_part += sentence + "\n"

            # إضافة الجزء الأخير
            if current_part:
                parts.append(current_part.strip())

            return parts if parts else [text[:max_length]]

        except Exception as e:
            logger.error(f"❌ خطأ في تقسيم النص: {e}")
            # تقسيم بسيط كبديل
            return [text[i:i+max_length] for i in range(0, len(text), max_length)]
